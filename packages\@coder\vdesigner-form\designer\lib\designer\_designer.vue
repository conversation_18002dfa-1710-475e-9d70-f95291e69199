<script setup lang="ts">
/**
 * 设计器主组件
 * 提供表单设计器的核心功能，包括组件面板、设计画布、属性设置等
 */

// ============================================================================
// 类型导入
// ============================================================================
import type { DesignerPropsType } from '@coder/vdesigner-core';
import type { CSSProperties } from 'vue';

// ============================================================================
// Vue 核心导入
// ============================================================================
import {
  onMounted,
  onUnmounted,
  ref,
  watch,
  nextTick,
  computed,
  shallowRef,
  markRaw,
  readonly
} from 'vue';

// ============================================================================
// 第三方库导入
// ============================================================================
import { TabPane, Tabs } from 'ant-design-vue';

// ============================================================================
// 核心功能导入
// ============================================================================
import { useMitter, useRenderStore } from '@coder/vdesigner-core';
import { useDesigner, useDesignerStore } from './hooks/index';

// ============================================================================
// 组件导入
// ============================================================================
import Layout from './layout/index.vue';
import DataSourcePresentation from './components/dataSourcePresentation/dataSourcePresentation.vue';
import HeaderTreeNode from './components/header/_treeNode.vue';
import Presentation from './components/presentation/presentation.vue';
import TemplatePanel from './components/template-panel/index.vue';
import WidgetPanel from './components/widget-panel/widget-panel.vue';
import WidgetSetting from './components/widget-setting/widget-setting.vue';

// ============================================================================
// 组件属性定义
// ============================================================================
interface DesignerProps extends DesignerPropsType {
  /** 自定义样式 */
  style?: CSSProperties | string;
}

const props = defineProps<DesignerProps>();

// ============================================================================
// 组合式函数和状态管理
// ============================================================================
const { renderId } = useDesigner(props);
const renderStore = useRenderStore(renderId.value);
const designerStore = useDesignerStore(renderId.value);

// ============================================================================
// 响应式状态
// ============================================================================
const presentationRef = ref<InstanceType<typeof Presentation>>();

// ============================================================================
// 性能优化和缓存策略
// ============================================================================

// 组件缓存 - 使用 shallowRef 避免深度响应式
const componentCache = shallowRef(new Map<string, any>());

// 配置缓存 - 使用 markRaw 标记为非响应式
const configCache = markRaw({
  lastDataSource: null as any,
  lastRootWidget: null as any,
  lastImplement: '',
  cacheTimestamp: 0,
});

// 防抖延迟
const DEBOUNCE_DELAY = 300;
let debounceTimer: number | null = null;

// 计算属性 - 缓存渲染配置
const cachedRenderConfig = computed(() => {
  if (!renderStore?.renderConfig) return null;

  const config = renderStore.renderConfig;
  const configKey = `${JSON.stringify(config.dataSource)}_${JSON.stringify(config.rootWidget)}`;

  // 检查缓存
  if (componentCache.value.has(configKey)) {
    return componentCache.value.get(configKey);
  }

  // 创建只读配置副本
  const readonlyConfig = readonly({
    dataSource: config.dataSource,
    rootWidget: config.rootWidget,
  });

  // 缓存配置
  componentCache.value.set(configKey, readonlyConfig);

  // 限制缓存大小
  if (componentCache.value.size > 10) {
    const firstKey = componentCache.value.keys().next().value;
    componentCache.value.delete(firstKey);
  }

  return readonlyConfig;
});

// 防抖函数
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  return (...args: Parameters<T>) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    debounceTimer = window.setTimeout(() => func(...args), delay);
  };
};

// 缓存清理函数
const clearCache = (): void => {
  componentCache.value.clear();
  configCache.lastDataSource = null;
  configCache.lastRootWidget = null;
  configCache.lastImplement = '';
  configCache.cacheTimestamp = 0;
};

// 检查配置是否发生变化
const hasConfigChanged = (newDataSource: any, newRootWidget: any): boolean => {
  const dataSourceChanged = JSON.stringify(newDataSource) !== JSON.stringify(configCache.lastDataSource);
  const rootWidgetChanged = JSON.stringify(newRootWidget) !== JSON.stringify(configCache.lastRootWidget);

  return dataSourceChanged || rootWidgetChanged;
};

// 更新配置缓存
const updateConfigCache = (dataSource: any, rootWidget: any): void => {
  configCache.lastDataSource = dataSource;
  configCache.lastRootWidget = rootWidget;
  configCache.cacheTimestamp = Date.now();
};

// ============================================================================
// 响应式监听器
// ============================================================================
const stopDevWatcher = watch(
  () => renderStore.isDev,
  (isDev: boolean) => {
    try {
      renderStore.setDev(isDev);
    } catch (error) {
      console.error('Failed to set dev mode:', error);
    }
  },
  { immediate: true }
);

// ============================================================================
// 事件处理器类型定义
// ============================================================================
type EventHandler = () => void;
type WidgetEventArgs = { widget: any };

// ============================================================================
// 事件处理器引用
// ============================================================================
let removeAddWidgetHandler: EventHandler | null = null;
let removeDeleteHandler: EventHandler | null = null;
let isComponentMounted = false;

// ============================================================================
// 事件处理函数（带性能优化）
// ============================================================================
const handleAddWidget = (args: WidgetEventArgs): void => {
  try {
    if (!isComponentMounted) return;

    if (args.widget?.options?.name) {
      designerStore.formNameCollections.set(
        args.widget,
        args.widget.options.name,
      );
    }
  } catch (error) {
    console.error('Failed to handle add widget event:', error);
  }
};

const handleDeleteWidget = (args: WidgetEventArgs): void => {
  try {
    if (!isComponentMounted) return;

    if (args.widget?.options?.name) {
      designerStore.formNameCollections.delete(args.widget);
    }
  } catch (error) {
    console.error('Failed to handle delete widget event:', error);
  }
};

// 防抖版本的事件处理函数
const debouncedHandleAddWidget = debounce(handleAddWidget, DEBOUNCE_DELAY);
const debouncedHandleDeleteWidget = debounce(handleDeleteWidget, DEBOUNCE_DELAY);

// ============================================================================
// 事件监听器初始化（带性能优化）
// ============================================================================
const initializeEventListeners = (): void => {
  try {
    const mitter = useMitter(renderId.value);

    // 注册组件添加事件监听器（使用防抖版本）
    removeAddWidgetHandler = mitter.onAddWidget(debouncedHandleAddWidget);

    // 注册组件删除事件监听器（使用防抖版本）
    removeDeleteHandler = mitter.onDeleteWidget(debouncedHandleDeleteWidget);

  } catch (error) {
    console.error('Failed to initialize designer event handlers:', error);
    throw error;
  }
};

// ============================================================================
// 事件监听器清理
// ============================================================================
const cleanupEventListeners = (): void => {
  try {
    removeAddWidgetHandler?.();
    removeDeleteHandler?.();
  } catch (error) {
    console.error('Failed to cleanup designer event handlers:', error);
  } finally {
    removeAddWidgetHandler = null;
    removeDeleteHandler = null;
  }
};

// ============================================================================
// 生命周期钩子
// ============================================================================
onMounted(async () => {
  try {
    isComponentMounted = true;

    // 初始化事件监听器
    initializeEventListeners();

    // 等待下一个 tick 确保所有组件都已挂载
    await nextTick();

  } catch (error) {
    console.error('Failed to mount designer component:', error);
    isComponentMounted = false;
  }
});

onUnmounted(() => {
  try {
    isComponentMounted = false;

    // 停止监听器
    stopDevWatcher();

    // 清理事件监听器
    cleanupEventListeners();

    // 清理缓存
    clearCache();

    // 清理防抖定时器
    if (debounceTimer) {
      clearTimeout(debounceTimer);
      debounceTimer = null;
    }

  } catch (error) {
    console.error('Failed to unmount designer component:', error);
  }
});
// ============================================================================
// 错误处理和状态管理
// ============================================================================
const isLoading = ref(false);
const errorState = ref<string | null>(null);

// ============================================================================
// 错误处理函数
// ============================================================================
const handleError = (error: unknown, context: string): void => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  console.error(`[Designer Error] ${context}:`, error);
  errorState.value = `${context}: ${errorMessage}`;

  // 可以在这里添加用户通知逻辑
  // message.error(`操作失败: ${errorMessage}`);
};

const clearError = (): void => {
  errorState.value = null;
};

// ============================================================================
// 安全执行函数
// ============================================================================
const safeExecute = async <T>(
  operation: () => Promise<T> | T,
  context: string,
  fallback?: T
): Promise<T | undefined> => {
  try {
    clearError();
    isLoading.value = true;
    return await operation();
  } catch (error) {
    handleError(error, context);
    return fallback;
  } finally {
    isLoading.value = false;
  }
};

// ============================================================================
// 组件暴露的方法类型定义
// ============================================================================
interface DesignerExposed {
  /** 清空设计器 */
  clearDesigner: () => Promise<boolean>;
  /** 获取设计数据 */
  getDesignData: () => typeof renderStore.renderConfig | null;
  /** 设置设计数据 */
  setDesignData: (data: { dataSource?: any[]; rootWidget?: any }) => Promise<boolean>;
  /** 获取错误状态 */
  getErrorState: () => string | null;
  /** 清除错误状态 */
  clearError: () => void;
  /** 获取加载状态 */
  isLoading: () => boolean;
  /** 清理缓存 */
  clearCache: () => void;
  /** 获取缓存统计信息 */
  getCacheStats: () => { size: number; timestamp: number };
  /** 获取缓存的渲染配置 */
  getCachedConfig: () => any;
}

// ============================================================================
// 组件暴露的方法实现
// ============================================================================
const clearDesigner = async (): Promise<boolean> => {
  return await safeExecute(
    async () => {
      if (!renderStore) {
        throw new Error('Render store is not available');
      }

      renderStore.clearWidget();
      renderStore.clearDataSource();

      // 等待清理完成
      await nextTick();
      return true;
    },
    '清空设计器',
    false
  ) ?? false;
};

const getDesignData = () => {
  try {
    if (!renderStore?.renderConfig) {
      console.warn('Render store or config is not available');
      return null;
    }
    return renderStore.renderConfig;
  } catch (error) {
    handleError(error, '获取设计数据');
    return null;
  }
};

const setDesignData = async (data: { dataSource?: any[]; rootWidget?: any }): Promise<boolean> => {
  return await safeExecute(
    async () => {
      // 参数验证
      if (!data || (data.dataSource === undefined && data.rootWidget === undefined)) {
        throw new Error('Invalid data: dataSource or rootWidget must be provided');
      }

      if (!renderStore?.renderConfig) {
        throw new Error('Render store or config is not available');
      }

      // 性能优化：检查数据是否真的发生了变化
      const newDataSource = data.dataSource ?? renderStore.renderConfig.dataSource;
      const newRootWidget = data.rootWidget ?? renderStore.renderConfig.rootWidget;

      if (!hasConfigChanged(newDataSource, newRootWidget)) {
        console.log('Data unchanged, skipping update');
        return true;
      }

      // 数据设置
      if (data.dataSource !== undefined) {
        if (!Array.isArray(data.dataSource)) {
          throw new Error('dataSource must be an array');
        }
        renderStore.renderConfig.dataSource = data.dataSource;
      }

      if (data.rootWidget !== undefined) {
        renderStore.renderConfig.rootWidget = data.rootWidget;
      }

      // 更新缓存
      updateConfigCache(newDataSource, newRootWidget);

      // 等待数据更新完成
      await nextTick();

      // 性能优化：只在必要时触发重新渲染
      if (presentationRef.value && renderStore.implement !== undefined) {
        const currentImplement = renderStore.implement;

        // 检查 implement 是否发生变化
        if (configCache.lastImplement !== currentImplement) {
          renderStore.implement = '';
          await nextTick();
          renderStore.implement = currentImplement;
          configCache.lastImplement = currentImplement;
          await nextTick();
        }
      }

      return true;
    },
    '设置设计数据',
    false
  ) ?? false;
};

// 暴露组件方法
defineExpose<DesignerExposed>({
  clearDesigner,
  getDesignData,
  setDesignData,
  getErrorState: () => errorState.value,
  clearError,
  isLoading: () => isLoading.value,
  clearCache,
  getCacheStats: () => ({
    size: componentCache.value.size,
    timestamp: configCache.cacheTimestamp,
  }),
  getCachedConfig: () => cachedRenderConfig.value,
});
</script>

<template>
  <Layout
    :style="props.style"
    :render-id="renderId"
    class="vdesigner-main"
  >
    <!-- 左侧面板：组件库、组件树、模板 -->
    <template #left>
      <Tabs
        class="bg-background text-foreground designer-tabs"
        size="small"
        type="card"
      >
        <TabPane key="widgets" tab="组件库">
          <div class="panel-content">
            <WidgetPanel />
          </div>
        </TabPane>

        <TabPane key="treePanel" tab="组件树">
          <div class="panel-content">
            <HeaderTreeNode :render-id="renderId" />
          </div>
        </TabPane>

        <TabPane key="template" tab="模板">
          <div class="panel-content">
            <TemplatePanel :render-id="renderId" />
          </div>
        </TabPane>
      </Tabs>
    </template>

    <!-- 中央设计区域 -->
    <template #widgets>
      <div class="design-area">
        <Presentation
          :render-id="renderId"
          ref="presentationRef"
          class="presentation-container"
        >
          <template #default>
            <slot name="menuBar" />
          </template>
        </Presentation>
      </div>
    </template>

    <!-- 底部数据源面板 -->
    <template #dataSourceSet>
      <div class="data-source-panel">
        <DataSourcePresentation :render-id="renderId" />
      </div>
    </template>

    <!-- 右侧属性面板 -->
    <template #right>
      <div class="properties-panel">
        <WidgetSetting
          :render-id="renderId"
          class="widget-setting-container"
        />
      </div>
    </template>
  </Layout>
</template>

<style lang="scss" scoped>

// 导入基础样式
// ============================================================================
@import './designer.scss';

// ============================================================================
// 响应式设计
// ============================================================================

@media (max-width: 768px) {
  .panel-content {
    height: calc(100vh - 100px);
    padding: 4px;
  }

  .properties-panel .widget-setting-container {
    padding: 4px 8px;
  }
}

// ============================================================================
// 组件特定样式
// ============================================================================

.vdesigner-main {
  height: 100%;

  :deep(.designer-tabs) {
    .ant-tabs-content-holder {
      padding: 0;
    }

    .ant-tabs-tabpane {
      height: 100%;
      overflow: hidden;
    }
  }
}

.panel-content {
  height: calc(100vh - 120px);
  padding: 8px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--designer-bg-light);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--designer-border-base);
    border-radius: 3px;

    &:hover {
      background: var(--designer-primary);
    }
  }
}

.design-area {
  display: flex;
  flex-direction: column;
  height: 100%;

  .presentation-container {
    flex: 1;
    overflow: hidden;
  }
}

.data-source-panel {
  height: 100%;
  padding: 8px;
  background: var(--designer-bg-light);
  border-top: 1px solid var(--designer-border-light);
}

.properties-panel {
  height: 100%;
  background: var(--designer-bg-base);

  .widget-setting-container {
    height: 100%;
    padding: 8px 12px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--designer-bg-light);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--designer-border-base);
      border-radius: 3px;

      &:hover {
        background: var(--designer-primary);
      }
    }
  }
}

// ============================================================================
// 深色主题适配
// ============================================================================

:deep(.ant-tabs-dark) {
  .ant-tabs-tab {
    color: var(--designer-text-secondary);

    &.ant-tabs-tab-active {
      color: var(--designer-primary);
    }
  }
}// ============================================================================
</style>
